<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Explore Sri Lanka: Tourist Routes</title>
    <script src="https://cdn.tailwindcss.com/3.4.16"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Pacifico&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/remixicon/4.6.0/remixicon.min.css" rel="stylesheet">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#22c55e',
                        secondary: '#16a34a'
                    },
                    borderRadius: {
                        'none': '0px',
                        'sm': '4px',
                        DEFAULT: '8px',
                        'md': '12px',
                        'lg': '16px',
                        'xl': '20px',
                        '2xl': '24px',
                        '3xl': '32px',
                        'full': '9999px',
                        'button': '8px'
                    }
                }
            }
        }
    </script>
    <style>
        :where([class^="ri-"])::before {
            content: "\f3c2";
        }
        .hero-bg {
            background-image: url('imgs/4917ef852d8d2052b452851c23c22b9b.jpg');
            background-size: cover;
            background-position: center;
            background-repeat: no-repeat;
        }
        .map-container {
            background-image: url('imgs/pexels-ollivves-1078983.jpg');
            background-size: contain;
            background-position: center;
            background-repeat: no-repeat;
        }
    </style>
</head>
<body class="bg-white">
        <header class="fixed top-0 left-0 right-0 w-full z-50 bg-white/10 backdrop-blur-lg" data-navbar>
        <nav id="mainNav" class="flex items-center justify-between px-6 py-4 max-w-7xl mx-auto">
            <div>
                <img src="imgs/turtlelogo 1.png" alt="Turtle Isle Ventures Logo" class="h-10">
            </div>
           <div class="hidden md:flex items-center space-x-8">
                <a href="index.html" class="text-white hover:text-gray-200 transition-colors">Home</a>

                <a href="stay.html" class="text-white hover:text-gray-200 transition-colors">Stay</a>
                <a href="explore.html" class="text-white hover:text-gray-200 transition-colors">Explore</a>
                <a href="contact.html" class="text-white hover:text-gray-200 transition-colors">Contact</a>
                <a href="#" class="text-white hover:text-gray-200 transition-colors">About Us</a>
            </div>
            <div class="flex items-center space-x-4">
                <!-- Custom language selector -->
                <div class="relative group">
                    <button type="button" class="flex items-center bg-white/20 text-white text-sm rounded px-3 py-1 outline-none border-none focus:ring-2 focus:ring-primary gap-2 min-w-[140px]">
                        <img src="imgs/us-flag.png" alt="English(US)" class="w-6 h-6 rounded-full object-cover" />
                        <span>English(US)</span>
                        <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
                            <path d="M6 9l6 6 6-6"/>
                        </svg>
                    </button>
                    <!-- Dropdown menu, hidden by default, shown on hover/focus -->
                    <div class="absolute left-0 mt-2 w-40 bg-white rounded shadow-lg z-10 hidden group-hover:block group-focus:block">
                        <button class="flex items-center w-full px-3 py-2 text-gray-800 hover:bg-gray-100 gap-2">
                            <img src="imgs/us-flag.png" alt="English(US)" class="w-5 h-5 rounded-full object-cover" />
                            English(US)
                        </button>
                        <button class="flex items-center w-full px-3 py-2 text-gray-800 hover:bg-gray-100 gap-2">
                            <img src="imgs/es-flag.png" alt="Español" class="w-5 h-5 rounded-full object-cover" />
                            Español
                        </button>
                        <button class="flex items-center w-full px-3 py-2 text-gray-800 hover:bg-gray-100 gap-2">
                            <img src="imgs/fr-flag.png" alt="Français" class="w-5 h-5 rounded-full object-cover" />
                            Français
                        </button>
                        <!-- Add more languages/flags as needed -->
                    </div>
                </div>
            </div>
        </nav>
    </header>
    <section class="hero-bg min-h-screen flex items-center justify-center relative">
        <div class="absolute inset-0 bg-black/30"></div>
        <div class="relative z-10 text-center text-white px-6 max-w-4xl">
            <p class="text-lg mb-4 opacity-90">Explore</p>
            <h1 class="text-5xl md:text-6xl font-bold mb-6 leading-tight">
                Explore Sri Lanka:<br>
                Tourist Routes
            </h1>
            <p class="text-lg md:text-xl opacity-90 max-w-2xl mx-auto leading-relaxed">
                Uncover Sri Lanka's breathtaking landscapes and cultural gems with curated travel routes. From ancient cities to coastal escapes and misty hill country, each path reveals a unique side of this island paradise.
            </p>
        </div>
    </section>

    <main class="py-16 px-6 ">
        <div class="max-w-7xl mx-auto">
            <div class="mb-8">
                <a href="explore.html" class="inline-flex items-center text-gray-600 hover:text-primary transition-colors">
                    <div class="w-4 h-4 flex items-center justify-center mr-2">
                        <i class="ri-arrow-left-line"></i>
                    </div>
                    Back to recommended list
                </a>
            </div>

            <div class="grid lg:grid-cols-2 gap-12 items-start">
                <div>
                    <div class="mb-6">
                        <p class="text-primary text-sm font-medium mb-2">TRAVEL PLAN</p>
                        <h2 class="text-3xl font-bold text-gray-900 mb-6">Kandy and hill country</h2>
                    </div>

                    <div class="space-y-8">
                        <div class="flex">
                            <div class="flex-shrink-0 w-12 h-12 bg-primary text-white rounded-full flex items-center justify-center font-bold mr-4">
                                1
                            </div>
                            <div>
                                <h3 class="font-semibold text-gray-900 mb-2">Day 1 - Sigiriya</h3>
                                <p class="text-gray-600 leading-relaxed">
                                    Climb the iconic Lion Rock Fortress and explore ancient frescoes, landscaped gardens, and breathtaking panoramic views. A must-visit for history lovers and adventure seekers alike.
                                </p>
                            </div>
                        </div>

                        <div class="flex">
                            <div class="flex-shrink-0 w-12 h-12 bg-primary text-white rounded-full flex items-center justify-center font-bold mr-4">
                                2
                            </div>
                            <div>
                                <h3 class="font-semibold text-gray-900 mb-2">Day 2 - Dambulla Matugama</h3>
                                <p class="text-gray-600 leading-relaxed">
                                    Step into serenity at the Buddhist temples, home to Sri Lanka's most revered relics. Witness cultural rituals, traditional drumming, and the temple's stunning golden roof reflected in the lake.
                                </p>
                            </div>
                        </div>

                        <div class="flex">
                            <div class="flex-shrink-0 w-12 h-12 bg-primary text-white rounded-full flex items-center justify-center font-bold mr-4">
                                3
                            </div>
                            <div>
                                <h3 class="font-semibold text-gray-900 mb-2">Day 3 - Nine Arch Bridge & Ravana Falls, Ella</h3>
                                <p class="text-gray-600 leading-relaxed">
                                    Marvel at the engineering beauty of the Nine Arch Bridge as train glides through misty hills, then visit Ravana Falls – a powerful cascade steeped in legend and perfect for a scenic stop.
                                </p>
                            </div>
                        </div>
                    </div>
                    <div class="mt-10 pt-8 border-t border-gray-200">
                        <div class="flex items-center justify-between mb-6">
                            <span class="text-lg font-semibold text-gray-900">Total 3 day package</span>
                            <span class="text-2xl font-bold text-gray-900">$1000</span>
                        </div>
                        <button class="w-full bg-primary hover:bg-secondary text-white font-semibold py-4 px-8 !rounded-button transition-colors whitespace-nowrap">
                            Book Now
                        </button>
                    </div>
                </div>

                <div class="lg:sticky lg:top-24">
                    <div class="map-container aspect-[4/3] rounded-xl bg-blue-100 flex items-center justify-center">
                        <img src="imgs/24a18cb4b1291681cdd6940cba95927f.jpg" 
                             alt="Sri Lanka Tourist Map" 
                             class="w-full h-full object-contain rounded-xl">
                    </div>
                </div>
            </div>
        </div>
    </main>

    <section class="py-16 px-6 bg-gray-50">
        <div class="max-w-7xl mx-auto">
            <div class="mb-12">
                <p class="text-primary text-sm font-medium mb-2">LUXURY EXPERIENCE</p>
                <h2 class="text-3xl font-bold text-gray-900">Our Hotels</h2>
            </div>

            <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
                <div class="bg-white rounded-xl overflow-hidden shadow-sm hover:shadow-md transition-shadow">
                    <div class="aspect-[4/3] bg-gray-200">
                        <img src="imgs/15f12d5930455d5f45d94fb4e05eb7a4.jpg" 
                             alt="Rock de gant Hotel" 
                             class="w-full h-full object-cover object-top">
                    </div>
                    <div class="p-6">
                        <p class="text-gray-600 text-sm mb-2">From $190/night</p>
                        <h3 class="text-xl font-semibold text-gray-900">Rock de gant</h3>
                    </div>
                </div>
                <div class="bg-white rounded-xl overflow-hidden shadow-sm hover:shadow-md transition-shadow">
                    <div class="aspect-[4/3] bg-gray-200">
                        <img src="imgs/635c176170dc98643365a55817a36b5e.jpg" 
                             alt="Serene Bay Retreat" 
                             class="w-full h-full object-cover object-top">
                    </div>
                    <div class="p-6">
                        <p class="text-gray-600 text-sm mb-2">From $150/night</p>
                        <h3 class="text-xl font-semibold text-gray-900">Serene Bay Retreat</h3>
                    </div>
                </div>
                <div class="bg-white rounded-xl overflow-hidden shadow-sm hover:shadow-md transition-shadow">
                    <div class="aspect-[4/3] bg-gray-200">
                        <img src="imgs/c2b2740c9237b9159c49b1153ac3a089.jpg" 
                             alt="Coastal Breeze Inn" 
                             class="w-full h-full object-cover object-top">
                    </div>
                    <div class="p-6">
                        <p class="text-gray-600 text-sm mb-2">From $120/night</p>
                        <h3 class="text-xl font-semibold text-gray-900">Coastal Breeze Inn</h3>
                    </div>
                </div>
            </div>

            <div class="mt-12 text-center">
                <button class="bg-green-900 hover:bg-secondary text-white font-semibold py-3 px-8 !rounded-button transition-colors whitespace-nowrap inline-flex items-center">
                    View All Hotels
                    <div class="w-4 h-4 flex items-center justify-center ml-2">
                        <i class="ri-arrow-right-line"></i>
                    </div>
                </button>
            </div>
        </div>
    </section>

    <footer class="bg-green-900 text-green-900 py-8 relative" style="background-image: url('imgs/Frame 53.png'); background-size: cover; background-position: center;">
        <div class="absolute inset-0 opacity-80 pointer-events-none"></div>
        <div class="max-w-7xl mx-auto px-6 relative z-10">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                <div>
                    <div class="mb-6">
                        <img src="imgs/turtlelogo 1.png" alt="Turtle Isle Ventures Logo" class="h-8">
                    </div>
                   <div class="space-y-2">
                        <p class="flex items-center">
                            <span class="w-5 h-5 flex items-center justify-center mr-3">
                                <i class="ri-map-pin-line"></i>
                            </span>
                            123 Paradise Island Drive
                        </p>
                        <p class="flex items-center">
                            <span class="w-5 h-5 flex items-center justify-center mr-3">
                                <i class="ri-phone-line"></i>
                            </span>
                            +****************
                        </p>
                        <p class="flex items-center">
                            <span class="w-5 h-5 flex items-center justify-center mr-3">
                                <i class="ri-mail-line"></i>
                            </span>
                            <EMAIL>
                        </p>
                    </div>
                </div>
                
                <div>
                    <h3 class="text-lg font-semibold mb-4">Quick Links</h3>
                    <ul class="space-y-2">
                        <li><a href="index.html" class="hover:text-gray-300 transition-colors">Home</a></li>
                        <li><a href="#" class="hover:text-gray-300 transition-colors">About</a></li>
                        <li><a href="#" class="hover:text-gray-300 transition-colors">Tours</a></li>
                        <li><a href="stay.html" class="hover:text-gray-300 transition-colors">Hotels</a></li>
                        <li><a href="#" class="hover:text-gray-300 transition-colors">Contact</a></li>
                    </ul>
                </div>
                
                <div>
                    <h3 class="text-lg font-semibold mb-4">Services</h3>
                    <ul class="space-y-2">
                        <li><a href="#" class="hover:text-gray-300 transition-colors">Snorkeling Tours</a></li>
                        <li><a href="#" class="hover:text-gray-300 transition-colors">Island Hopping</a></li>
                        <li><a href="#" class="hover:text-gray-300 transition-colors">Spa Treatments</a></li>
                        <li><a href="#" class="hover:text-gray-300 transition-colors">Dining</a></li>
                        <li><a href="#" class="hover:text-gray-300 transition-colors">Adventure Sports</a></li>
                    </ul>
                </div>
                
                <div>
                    <h3 class="text-lg font-semibold mb-4">Follow Us</h3>
                    <div class="flex space-x-4">
                        <div class="w-8 h-8 flex items-center justify-center">
                            <i class="ri-facebook-line ri-lg hover:text-gray-300 transition-colors cursor-pointer"></i>
                        </div>
                        <div class="w-8 h-8 flex items-center justify-center">
                            <i class="ri-twitter-line ri-lg hover:text-gray-300 transition-colors cursor-pointer"></i>
                        </div>
                        <div class="w-8 h-8 flex items-center justify-center">
                            <i class="ri-instagram-line ri-lg hover:text-gray-300 transition-colors cursor-pointer"></i>
                        </div>
                        <div class="w-8 h-8 flex items-center justify-center">
                            <i class="ri-youtube-line ri-lg hover:text-gray-300 transition-colors cursor-pointer"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </footer>
    <div class="bg-green-900 border-t border-green-800 py-4 text-center">
        <p class="text-gray-300 m-0">&copy; 2025 Turtle Isle Ventures. All rights reserved.</p>
    </div>

    <script id="navigation-functionality">
        document.addEventListener('DOMContentLoaded', function() {
            const languageBtn = document.getElementById('languageBtn');
            const languageDropdown = document.getElementById('languageDropdown');
            const mobileMenuBtn = document.getElementById('mobileMenuBtn');
            const mobileMenu = document.getElementById('mobileMenu');
            languageBtn.addEventListener('click', function(e) {
                e.preventDefault();
                languageDropdown.classList.toggle('hidden');
            });

            mobileMenuBtn.addEventListener('click', function() {
                mobileMenu.classList.toggle('hidden');
            });

            document.addEventListener('click', function(e) {
                if (!languageBtn.contains(e.target) && !languageDropdown.contains(e.target)) {
                    languageDropdown.classList.add('hidden');
                }
            });
        });
    </script>

    <script id="smooth-scrolling">
        document.addEventListener('DOMContentLoaded', function() {
            const links = document.querySelectorAll('a[href^="#"]');
            
            links.forEach(link => {
                link.addEventListener('click', function(e) {
                    e.preventDefault();
                    const targetId = this.getAttribute('href');
                    const targetElement = document.querySelector(targetId);
                    
                    if (targetElement) {
                        targetElement.scrollIntoView({
                            behavior: 'smooth',
                            block: 'start'
                        });
                    }
                });
            });
        });
    </script>

    <script id="booking-interaction">
        document.addEventListener('DOMContentLoaded', function() {
            const bookNowBtn = document.querySelector('button:contains("Book Now")');
            const viewAllHotelsBtn = document.querySelector('button:contains("View All Hotels")');
            
            if (bookNowBtn) {
                bookNowBtn.addEventListener('click', function() {
                    alert('Booking functionality would be implemented here. Redirecting to booking page...');
                });
            }
            
            if (viewAllHotelsBtn) {
                viewAllHotelsBtn.addEventListener('click', function() {
                    alert('Redirecting to hotels page...');
                });
            }
        });
    </script>
    <script src="navbar-color.js"></script>
</body>
</html>