:where([class^="ri-"])::before {
    content: "\f3c2";
}
.hero-bg {
    background-image: url('imgs/turtles-swimming-ocean.jpg');
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
}
.hero-overlay {
    background: linear-gradient(135deg, rgba(0,0,0,0.45) 0%, rgba(0,0,0,0.25) 100%);
}
/* .hero-bg .text-white {
    text-shadow: 0 2px 8px rgba(0,0,0,0.7), 0 1px 2px rgba(0,0,0,0.5);
} */
.btn-olive {
    background-color: #3e7343 !important;
    color: #fff !important;
    border-radius: 32px !important;
    transition: background 0.2s;
    padding-top: 0.5rem !important;
    padding-bottom: 0.5rem !important;
}
.btn-olive:hover {
    background-color: #2f5132 !important;
}
.second-page-bg {
    background-image: url('imgs/2ndpage-bg.png');
    background-size: cover;
    background-repeat: no-repeat;
    background-position: center;
} 
.why-us-bg {
    background: url('imgs/Frame 46.png') no-repeat center center;
    background-size: cover;
    /* TODO: Replace 1920 and 500 with your image's actual width and height for the correct aspect ratio */
    aspect-ratio: 1920 / 500;
    display: flex;
    align-items: center;
}
.our-hotels-bg {
    background: url('imgs/2ndpage-bg2.png') no-repeat center center;
    background-size: cover;
    display: flex;
    align-items: center;
}