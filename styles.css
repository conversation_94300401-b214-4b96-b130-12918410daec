:where([class^="ri-"])::before {
    content: "\f3c2";
}
/* Hero Carousel Styles */
.hero-carousel-section {
    position: relative;
}

.hero-slide {
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    height: 100vh;
    position: relative;
}

.hero-slide::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(22, 163, 74, 0.2) 0%, rgba(0, 0, 0, 0.3) 50%, rgba(4, 120, 87, 0.2) 100%);
    z-index: 1;
}

.hero-overlay {
    background: linear-gradient(135deg, rgba(0,0,0,0.5) 0%, rgba(0,0,0,0.3) 50%, rgba(0,0,0,0.4) 100%);
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 2;
}

.carousel-caption {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 3;
    color: white;
}

.carousel-fade .carousel-item {
    opacity: 0;
    transition-duration: 0.6s;
    transition-property: opacity;
}

.carousel-fade .carousel-item.active,
.carousel-fade .carousel-item-next.carousel-item-start,
.carousel-fade .carousel-item-prev.carousel-item-end {
    opacity: 1;
}

.carousel-fade .carousel-item-next,
.carousel-fade .carousel-item-prev {
    transform: translateX(0);
}

/* Custom carousel controls */
.carousel-control-prev,
.carousel-control-next {
    width: 5%;
    opacity: 0.7;
}

.carousel-control-prev:hover,
.carousel-control-next:hover {
    opacity: 1;
}

.carousel-indicators {
    bottom: 30px;
}

.carousel-indicators [data-bs-target] {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.5);
    border: 2px solid rgba(255, 255, 255, 0.8);
}

.carousel-indicators .active {
    background-color: #16a34a;
    border-color: #16a34a;
}

/* Responsive carousel text */
@media (max-width: 768px) {
    .carousel-caption h1 {
        font-size: 2.5rem !important;
    }

    .carousel-caption p {
        font-size: 1rem !important;
        margin-bottom: 1.5rem !important;
    }

    .btn-olive {
        padding: 0.5rem 1.5rem !important;
        font-size: 0.9rem !important;
    }

    .carousel-indicators {
        bottom: 20px;
    }
}

@media (max-width: 480px) {
    .carousel-caption h1 {
        font-size: 2rem !important;
        margin-bottom: 1rem !important;
    }

    .carousel-caption p {
        font-size: 0.9rem !important;
        margin-bottom: 1rem !important;
    }
}
/* .hero-bg .text-white {
    text-shadow: 0 2px 8px rgba(0,0,0,0.7), 0 1px 2px rgba(0,0,0,0.5);
} */
.btn-olive {
    background-color: #3e7343 !important;
    color: #fff !important;
    border-radius: 32px !important;
    transition: background 0.2s;
    padding-top: 0.5rem !important;
    padding-bottom: 0.5rem !important;
}
.btn-olive:hover {
    background-color: #2f5132 !important;
}
.second-page-bg {
    background-image: url('imgs/2ndpage-bg.png');
    background-size: cover;
    background-repeat: no-repeat;
    background-position: center;
    position: relative;
}

.second-page-bg::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(255, 255, 255, 0.95) 0%, rgba(249, 250, 251, 0.9) 100%);
    z-index: 1;
}

.second-page-bg > * {
    position: relative;
    z-index: 2;
}
.why-us-bg {
    background: url('imgs/Frame 46.png') no-repeat center center;
    background-size: cover;
    aspect-ratio: 1920 / 500;
    display: flex;
    align-items: center;
    position: relative;
}

.why-us-bg::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(16, 185, 129, 0.1) 0%, rgba(255, 255, 255, 0.8) 50%, rgba(4, 120, 87, 0.1) 100%);
    z-index: 1;
}

.why-us-bg > * {
    position: relative;
    z-index: 2;
}
.our-hotels-bg {
    background: url('imgs/2ndpage-bg2.png') no-repeat center center;
    background-size: cover;
    display: flex;
    align-items: center;
}

/* Animation Styles */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes fadeInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

@keyframes scaleIn {
    from {
        opacity: 0;
        transform: scale(0.9);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

.animate-fade-in-up {
    animation: fadeInUp 0.8s ease-out forwards;
}

.animate-fade-in-left {
    animation: fadeInLeft 0.8s ease-out forwards;
}

.animate-fade-in-right {
    animation: fadeInRight 0.8s ease-out forwards;
}

.animate-fade-in {
    animation: fadeIn 0.8s ease-out forwards;
}

.animate-scale-in {
    animation: scaleIn 0.8s ease-out forwards;
}

.animate-delay-200 {
    animation-delay: 0.2s;
}

.animate-delay-400 {
    animation-delay: 0.4s;
}

.animate-delay-600 {
    animation-delay: 0.6s;
}

.animate-delay-800 {
    animation-delay: 0.8s;
}

/* Initial state for animated elements */
.animate-on-scroll {
    opacity: 0;
}

/* Smooth transitions for interactive elements */
.btn-olive {
    transition: all 0.3s ease;
}

.btn-olive:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(62, 115, 67, 0.3);
}

/* Card hover animations */
.card-hover {
    transition: all 0.3s ease;
}

.card-hover:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}



/* Enhanced background patterns and overlays */
body {
    background-color: #fafafa;
}

/* Subtle pattern overlay for sections */
.pattern-overlay {
    position: relative;
}

.pattern-overlay::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: radial-gradient(circle at 2px 2px, rgba(16, 185, 129, 0.05) 1px, transparent 0);
    background-size: 20px 20px;
    pointer-events: none;
    z-index: 1;
}

/* Enhanced card shadows and depth */
.enhanced-card {
    background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    border: 1px solid rgba(16, 185, 129, 0.1);
}

.enhanced-card:hover {
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    transform: translateY(-2px);
}

/* Gradient text effects */
.gradient-text {
    background: linear-gradient(135deg, #16a34a 0%, #059669 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Enhanced section dividers */
.section-divider {
    height: 2px;
    background: linear-gradient(90deg, transparent 0%, #16a34a 50%, transparent 100%);
    margin: 2rem 0;
}

/* Video Section Styles */
.video-container {
    position: relative;
    overflow: hidden;
}

.video-container video {
    transition: transform 0.3s ease;
}

.video-container:hover video {
    transform: scale(1.02);
}

.video-overlay {
    cursor: pointer;
}

.play-button {
    transform: scale(1);
    transition: transform 0.3s ease;
}

.play-button:hover {
    transform: scale(1.1);
}

/* Custom video controls styling */
video::-webkit-media-controls-panel {
    background-color: rgba(0, 0, 0, 0.8);
}

video::-webkit-media-controls-play-button,
video::-webkit-media-controls-volume-slider,
video::-webkit-media-controls-timeline {
    filter: invert(1);
}