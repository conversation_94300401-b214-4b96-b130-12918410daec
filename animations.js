// Scroll-triggered animations
document.addEventListener('DOMContentLoaded', function() {
    // Create intersection observer for scroll animations
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const element = entry.target;
                
                // Add the appropriate animation class based on the element's data attributes
                if (element.classList.contains('animate-fade-in-up')) {
                    element.style.opacity = '1';
                    element.style.animation = 'fadeInUp 0.8s ease-out forwards';
                } else if (element.classList.contains('animate-fade-in-left')) {
                    element.style.opacity = '1';
                    element.style.animation = 'fadeInLeft 0.8s ease-out forwards';
                } else if (element.classList.contains('animate-fade-in-right')) {
                    element.style.opacity = '1';
                    element.style.animation = 'fadeInRight 0.8s ease-out forwards';
                } else if (element.classList.contains('animate-fade-in')) {
                    element.style.opacity = '1';
                    element.style.animation = 'fadeIn 0.8s ease-out forwards';
                } else if (element.classList.contains('animate-scale-in')) {
                    element.style.opacity = '1';
                    element.style.animation = 'scaleIn 0.8s ease-out forwards';
                }

                // Apply animation delays
                if (element.classList.contains('animate-delay-200')) {
                    element.style.animationDelay = '0.2s';
                } else if (element.classList.contains('animate-delay-400')) {
                    element.style.animationDelay = '0.4s';
                } else if (element.classList.contains('animate-delay-600')) {
                    element.style.animationDelay = '0.6s';
                } else if (element.classList.contains('animate-delay-800')) {
                    element.style.animationDelay = '0.8s';
                }

                // Stop observing this element
                observer.unobserve(element);
            }
        });
    }, observerOptions);

    // Observe all elements with animation classes
    const animatedElements = document.querySelectorAll('.animate-on-scroll');
    animatedElements.forEach(element => {
        observer.observe(element);
    });

    // Add smooth scrolling for anchor links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });

    // Add parallax effect to hero section
    window.addEventListener('scroll', function() {
        const scrolled = window.pageYOffset;
        const heroSection = document.querySelector('.hero-bg');
        if (heroSection) {
            const rate = scrolled * -0.5;
            heroSection.style.transform = `translateY(${rate}px)`;
        }
    });

    // Add hover animations to buttons
    const buttons = document.querySelectorAll('.btn-olive');
    buttons.forEach(button => {
        button.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-2px)';
            this.style.boxShadow = '0 4px 12px rgba(62, 115, 67, 0.3)';
        });
        
        button.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
            this.style.boxShadow = 'none';
        });
    });

    // Add stagger animation for grid items
    const gridItems = document.querySelectorAll('.grid > div');
    gridItems.forEach((item, index) => {
        item.style.animationDelay = `${index * 0.1}s`;
    });
});

// Add loading animation
window.addEventListener('load', function() {
    document.body.classList.add('loaded');
    
    // Trigger hero animations after page load
    const heroElements = document.querySelectorAll('.hero-bg .animate-fade-in-up');
    heroElements.forEach((element, index) => {
        setTimeout(() => {
            element.style.opacity = '1';
            element.style.animation = `fadeInUp 0.8s ease-out forwards`;
        }, index * 200);
    });
});
