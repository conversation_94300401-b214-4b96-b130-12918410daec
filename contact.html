<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Contact Us - Turtle Isle Ventures</title>
    <script src="https://cdn.tailwindcss.com/3.4.16"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Pacifico&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/remixicon/4.6.0/remixicon.min.css">
    <link rel="stylesheet" href="styles.css">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#16a34a',
                        secondary: '#22c55e'
                    },
                    borderRadius: {
                        'none': '0px',
                        'sm': '4px',
                        DEFAULT: '8px',
                        'md': '12px',
                        'lg': '16px',
                        'xl': '20px',
                        '2xl': '24px',
                        '3xl': '32px',
                        'full': '9999px',
                        'button': '8px'
                    }
                }
            }
        }
    </script>
    <style>
        :where([class^="ri-"])::before {
            content: "\f3c2";
        }
    </style>
</head>
<body class="bg-white">
 <header class="fixed top-0 left-0 right-0 w-full z-50 bg-white/80 backdrop-blur-lg" data-navbar>
        <nav id="mainNav" class="flex items-center justify-between px-6 py-4 max-w-7xl mx-auto">
            <div>
                <img src="imgs/turtlelogo 1.png" alt="Turtle Isle Ventures Logo" class="h-10">
            </div>
           <div class="hidden md:flex items-center space-x-8">
                <a href="index.html" class="text-green-800 hover:text-green-600 transition-colors font-medium">Home</a>

                <a href="stay.html" class="text-green-800 hover:text-green-600 transition-colors font-medium">Stay</a>
                <a href="explore.html" class="text-green-800 hover:text-green-600 transition-colors font-medium">Explore</a>
                <a href="contact.html" class="text-green-800 hover:text-green-600 transition-colors font-medium">Contact</a>
                <a href="#" class="text-green-800 hover:text-green-600 transition-colors font-medium">About Us</a>
            </div>
            <div class="flex items-center space-x-4">
                <!-- Custom language selector -->
                <div class="relative group">
                    <button type="button" class="flex items-center bg-white/20 text-green-800 text-sm rounded px-3 py-1 outline-none border-none focus:ring-2 focus:ring-primary gap-2 min-w-[140px] font-medium">
                        <img src="imgs/us-flag.png" alt="English(US)" class="w-6 h-6 rounded-full object-cover" />
                        <span>English(US)</span>
                        <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
                            <path d="M6 9l6 6 6-6"/>
                        </svg>
                    </button>
                    <!-- Dropdown menu, hidden by default, shown on hover/focus -->
                    <div class="absolute left-0 mt-2 w-40 bg-white rounded shadow-lg z-10 hidden group-hover:block group-focus:block">
                        <button class="flex items-center w-full px-3 py-2 text-gray-800 hover:bg-gray-100 gap-2">
                            <img src="imgs/us-flag.png" alt="English(US)" class="w-5 h-5 rounded-full object-cover" />
                            English(US)
                        </button>
                        <button class="flex items-center w-full px-3 py-2 text-gray-800 hover:bg-gray-100 gap-2">
                            <img src="imgs/es-flag.png" alt="Español" class="w-5 h-5 rounded-full object-cover" />
                            Español
                        </button>
                        <button class="flex items-center w-full px-3 py-2 text-gray-800 hover:bg-gray-100 gap-2">
                            <img src="imgs/fr-flag.png" alt="Français" class="w-5 h-5 rounded-full object-cover" />
                            Français
                        </button>
                        <!-- Add more languages/flags as needed -->
                    </div>
                </div>
            </div>
        </nav>
    </header>

    <main class="pt-20">
        <section class="relative min-h-96 flex items-center justify-center" style="background-image: url('imgs/1e0b5d8f8b52a9fedb10104a34c6af6c.jpg'); background-size: cover; background-position: center; background-repeat: no-repeat;">
            <div class="absolute inset-0 bg-gradient-to-r from-green-50/90 via-green-50/70 to-transparent"></div>
            <div class="relative z-10 text-center max-w-4xl mx-auto px-6">
                <h1 class="text-5xl md:text-6xl font-bold text-gray-900 mb-6">Contact Us</h1>
                <p class="text-lg text-gray-700 max-w-2xl mx-auto leading-relaxed">Have questions or need help planning your trip? We're here for you! Reach out to our team anytime—we'd love to hear from you. Plan your next adventure with Turtle ventures.</p>
            </div>
        </section>

        <section class="py-20 px-6 second-page-bg">
            <div class="max-w-7xl mx-auto">
                <div class="grid lg:grid-cols-2 gap-16 items-start">
                    <div class="bg-green-50 p-8 rounded-2xl">
                        <h2 class="text-2xl font-bold text-gray-900 mb-8">Get in touch</h2>
                        <form class="space-y-6">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Name</label>
                                <input type="text" placeholder="Enter your name" class="w-full px-4 py-3 border border-gray-200 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent outline-none transition-all text-sm">
                            </div>
                            
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Email</label>
                                <input type="email" placeholder="Enter your email" class="w-full px-4 py-3 border border-gray-200 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent outline-none transition-all text-sm">
                            </div>
                            
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Phone</label>
                                <input type="tel" placeholder="Enter your phone" class="w-full px-4 py-3 border border-gray-200 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent outline-none transition-all text-sm">
                            </div>
                            
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Message</label>
                                <textarea rows="4" placeholder="Your message" class="w-full px-4 py-3 border border-gray-200 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent outline-none transition-all resize-none text-sm"></textarea>
                            </div>
                            
                            <button type="submit" class="w-full bg-green-900 hover:bg-green-700 text-white font-medium py-3 px-6 !rounded-button transition-colors whitespace-nowrap">
                                Submit Now
                            </button>
                        </form>
                    </div>
                    
                    <div class="rounded-2xl overflow-hidden h-96 lg:h-full">
                        <img src="imgs/4f36a206c44f67bc1fcbfc4ff93db305.jpg" alt="Resort Contact" class="w-full h-full object-cover object-top">
                    </div>
                </div>
            </div>
        </section>

        <section class="py-16 px-6 bg-gray-50">
            <div class="max-w-6xl mx-auto">
                <div class="bg-white rounded-2xl p-8 shadow-sm">
                    <div class="grid md:grid-cols-3 gap-8 text-center">
                        <div>
                            <div class="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                                <i class="ri-home-4-line text-primary text-xl"></i>
                            </div>
                            <h3 class="font-semibold text-gray-900 mb-2">Locate Us</h3>
                            <p class="text-gray-600 text-sm">No. 100, Sample Road, Colombo 03</p>
                        </div>
                        
                        <div>
                            <div class="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                                <i class="ri-phone-line text-primary text-xl"></i>
                            </div>
                            <h3 class="font-semibold text-gray-900 mb-2">Call Us</h3>
                            <p class="text-gray-600 text-sm">+917 654 321 0</p>
                        </div>
                        
                        <div>
                            <div class="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                                <i class="ri-mail-line text-primary text-xl"></i>
                            </div>
                            <h3 class="font-semibold text-gray-900 mb-2">Write to Us</h3>
                            <p class="text-gray-600 text-sm"><EMAIL></p>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <footer class="bg-green-900 text-green-900 py-8 relative" style="background-image: url('imgs/Frame 53.png'); background-size: cover; background-position: center;">
        <div class="absolute inset-0 opacity-80 pointer-events-none"></div>
        <div class="max-w-7xl mx-auto px-6 relative z-10">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                <div>
                    <div class="mb-6">
                        <img src="imgs/turtlelogo 1.png" alt="Turtle Isle Ventures Logo" class="h-8">
                    </div>
                    <div class="space-y-2">
                        <p class="flex items-center">
                            <span class="w-5 h-5 flex items-center justify-center mr-3">
                                <i class="ri-map-pin-line"></i>
                            </span>
                            123 Paradise Island Drive
                        </p>
                        <p class="flex items-center">
                            <span class="w-5 h-5 flex items-center justify-center mr-3">
                                <i class="ri-phone-line"></i>
                            </span>
                            +****************
                        </p>
                        <p class="flex items-center">
                            <span class="w-5 h-5 flex items-center justify-center mr-3">
                                <i class="ri-mail-line"></i>
                            </span>
                            <EMAIL>
                        </p>
                    </div>
                </div>
                
                <div>
                    <h3 class="text-lg font-semibold mb-4">Quick Links</h3>
                    <ul class="space-y-2">
                        <li><a href="index.html" class="hover:text-gray-300 transition-colors">Home</a></li>
                        <li><a href="#" class="hover:text-gray-300 transition-colors">About</a></li>
                        <li><a href="#" class="hover:text-gray-300 transition-colors">Tours</a></li>
                        <li><a href="stay.html" class="hover:text-gray-300 transition-colors">Hotels</a></li>
                        <li><a href="#" class="hover:text-gray-300 transition-colors">Contact</a></li>
                    </ul>
                </div>
                
                <div>
                    <h3 class="text-lg font-semibold mb-4">Services</h3>
                    <ul class="space-y-2">
                        <li><a href="#" class="hover:text-gray-300 transition-colors">Snorkeling Tours</a></li>
                        <li><a href="#" class="hover:text-gray-300 transition-colors">Island Hopping</a></li>
                        <li><a href="#" class="hover:text-gray-300 transition-colors">Spa Treatments</a></li>
                        <li><a href="#" class="hover:text-gray-300 transition-colors">Dining</a></li>
                        <li><a href="#" class="hover:text-gray-300 transition-colors">Adventure Sports</a></li>
                    </ul>
                </div>
                
                <div>
                    <h3 class="text-lg font-semibold mb-4">Follow Us</h3>
                    <div class="flex space-x-4">
                        <div class="w-8 h-8 flex items-center justify-center">
                            <i class="ri-facebook-line ri-lg hover:text-gray-300 transition-colors cursor-pointer"></i>
                        </div>
                        <div class="w-8 h-8 flex items-center justify-center">
                            <i class="ri-twitter-line ri-lg hover:text-gray-300 transition-colors cursor-pointer"></i>
                        </div>
                        <div class="w-8 h-8 flex items-center justify-center">
                            <i class="ri-instagram-line ri-lg hover:text-gray-300 transition-colors cursor-pointer"></i>
                        </div>
                        <div class="w-8 h-8 flex items-center justify-center">
                            <i class="ri-youtube-line ri-lg hover:text-gray-300 transition-colors cursor-pointer"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <script id="mobile-menu-toggle">
        document.addEventListener('DOMContentLoaded', function() {
            const mobileMenuButton = document.getElementById('mobile-menu-button');
            const mobileMenu = document.getElementById('mobile-menu');
            
            mobileMenuButton.addEventListener('click', function() {
                mobileMenu.classList.toggle('hidden');
            });
        });
    </script>

    <script id="form-validation">
        document.addEventListener('DOMContentLoaded', function() {
            const form = document.querySelector('form');
            const inputs = form.querySelectorAll('input, textarea');
            
            form.addEventListener('submit', function(e) {
                e.preventDefault();
                
                let isValid = true;
                
                inputs.forEach(input => {
                    if (input.hasAttribute('required') && !input.value.trim()) {
                        isValid = false;
                        input.classList.add('border-red-500');
                    } else {
                        input.classList.remove('border-red-500');
                    }
                });
                
                if (isValid) {
                    alert('Thank you for your message! We will get back to you soon.');
                    form.reset();
                }
            });
            
            inputs.forEach(input => {
                input.addEventListener('input', function() {
                    if (this.classList.contains('border-red-500')) {
                        this.classList.remove('border-red-500');
                    }
                });
            });
        });
    </script>

    <script id="smooth-scroll">
        document.addEventListener('DOMContentLoaded', function() {
            document.querySelectorAll('a[href^="#"]').forEach(anchor => {
                anchor.addEventListener('click', function(e) {
                    e.preventDefault();
                    const target = document.querySelector(this.getAttribute('href'));
                    if (target) {
                        target.scrollIntoView({
                            behavior: 'smooth',
                            block: 'start'
                        });
                    }
                });
            });
        });
    </script>
</body>
</html>