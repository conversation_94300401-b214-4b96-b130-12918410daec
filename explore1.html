<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Explore Sri Lanka: Tourist Routes</title>
    <script src="https://cdn.tailwindcss.com/3.4.16"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#22c55e',
                        secondary: '#16a34a'
                    },
                    borderRadius: {
                        'none': '0px',
                        'sm': '4px',
                        DEFAULT: '8px',
                        'md': '12px',
                        'lg': '16px',
                        'xl': '20px',
                        '2xl': '24px',
                        '3xl': '32px',
                        'full': '9999px',
                        'button': '8px'
                    }
                }
            }
        }
    </script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Pacifico&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/remixicon/4.6.0/remixicon.min.css">
    <style>
        :where([class^="ri-"])::before {
            content: "\f3c2";
        }
        .hero-bg {
            background-image: url('imgs/5f43d5f980399f866722db32c3ccbded.jpg');
            background-size: cover;
            background-position: center;
            background-repeat: no-repeat;
        }
        .custom-select {
            appearance: none;
            background-image: url("imgs/pexels-ollivves-1078983.jpg");
            background-position: right 0.5rem center;
            background-repeat: no-repeat;
            background-size: 1.5em 1.5em;
            padding-right: 2.5rem;
        }
    </style>
</head>
<body class="bg-white">
    <header class="fixed top-0 left-0 right-0 w-full z-50 bg-white/10 backdrop-blur-lg" data-navbar>
        <nav id="mainNav" class="flex items-center justify-between px-6 py-4 max-w-7xl mx-auto">
            <div>
                <img src="imgs/turtlelogo 1.png" alt="Turtle Isle Ventures Logo" class="h-10">
            </div>
           <div class="hidden md:flex items-center space-x-8">
                <a href="index.html" class="text-white hover:text-gray-200 transition-colors">Home</a>

                <a href="stay.html" class="text-white hover:text-gray-200 transition-colors">Stay</a>
                <a href="explore.html" class="text-white hover:text-gray-200 transition-colors">Explore</a>
                <a href="contact.html" class="text-white hover:text-gray-200 transition-colors">Contact</a>
                <a href="#" class="text-white hover:text-gray-200 transition-colors">About Us</a>
            </div>
            <div class="flex items-center space-x-4">
                <!-- Custom language selector -->
                <div class="relative group">
                    <button type="button" class="flex items-center bg-white/20 text-white text-sm rounded px-3 py-1 outline-none border-none focus:ring-2 focus:ring-primary gap-2 min-w-[140px]">
                        <img src="imgs/us-flag.png" alt="English(US)" class="w-6 h-6 rounded-full object-cover" />
                        <span>English(US)</span>
                        <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
                            <path d="M6 9l6 6 6-6"/>
                        </svg>
                    </button>
                    <!-- Dropdown menu, hidden by default, shown on hover/focus -->
                    <div class="absolute left-0 mt-2 w-40 bg-white rounded shadow-lg z-10 hidden group-hover:block group-focus:block">
                        <button class="flex items-center w-full px-3 py-2 text-gray-800 hover:bg-gray-100 gap-2">
                            <img src="imgs/us-flag.png" alt="English(US)" class="w-5 h-5 rounded-full object-cover" />
                            English(US)
                        </button>
                        <button class="flex items-center w-full px-3 py-2 text-gray-800 hover:bg-gray-100 gap-2">
                            <img src="imgs/es-flag.png" alt="Español" class="w-5 h-5 rounded-full object-cover" />
                            Español
                        </button>
                        <button class="flex items-center w-full px-3 py-2 text-gray-800 hover:bg-gray-100 gap-2">
                            <img src="imgs/fr-flag.png" alt="Français" class="w-5 h-5 rounded-full object-cover" />
                            Français
                        </button>
                        <!-- Add more languages/flags as needed -->
                    </div>
                </div>
            </div>
        </nav>
    </header>

    <section class="hero-bg min-h-[600px] flex items-center relative ">
        <div class="absolute inset-0 bg-gradient-to-r from-white/90 via-white/60 to-transparent"></div>
        <div class="w-full px-6 relative z-10">
            <div class="max-w-7xl mx-auto">
                <div class="max-w-2xl">
                    <p class="text-primary font-medium mb-4 tracking-wide">Explore</p>
                    <h1 class="text-5xl md:text-6xl font-bold text-gray-900 mb-6 leading-tight">
                        Explore Sri Lanka:<br>
                        Tourist Routes
                    </h1>
                    <p class="text-xl text-gray-700 leading-relaxed max-w-xl">
                        Uncover Sri Lanka's breathtaking landscapes and cultural gems with curated travel routes. From ancient cities to coastal escapes and misty hill country, each path reveals a unique side of this island paradise.
                    </p>
                </div>
            </div>
        </div>
    </section>

    <section class="py-20 px-6 bg-gray-50 ">
        <div class="max-w-7xl mx-auto">
            <div class="grid lg:grid-cols-2 gap-16 items-center">
                <div>
                    <p class="text-primary font-medium mb-4 tracking-wide">CUSTOMIZE</p>
                    <h2 class="text-4xl font-bold text-gray-900 mb-8 leading-tight">
                        What are you interested to<br>
                        experience in the island?
                    </h2>
                    
                    <div class="space-y-6">
                        <div>
                            <label class="block text-gray-700 font-medium mb-3">Select your interests from the below list</label>
                            <select class="w-full px-4 py-3 border border-gray-200 rounded-lg bg-white text-gray-700 custom-select focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary">
                                <option>Ancient Temples & Cultural Heritage</option>
                                <option>Tea Plantations & Hill Country</option>
                                <option>Wildlife Safari & National Parks</option>
                                <option>Pristine Beaches & Water Sports</option>
                                <option>Adventure & Hiking Trails</option>
                                <option>Local Cuisine & Food Tours</option>
                            </select>
                        </div>
                        
                        <div>
                            <label class="block text-gray-700 font-medium mb-3">If you have specific interest you can type here</label>
                            <textarea 
                                placeholder="Your interests" 
                                rows="4" 
                                class="w-full px-4 py-3 border border-gray-200 rounded-lg bg-white text-gray-700 resize-none focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary"
                            ></textarea>
                        </div>
                        
                        <button class="bg-primary hover:bg-secondary text-white px-8 py-3 rounded-button font-medium transition-colors whitespace-nowrap !rounded-button">
                            Find My Curated Adventures
                        </button>
                    </div>
                </div>
                
                <div class="relative">
                    <div class="bg-blue-400 rounded-2xl p-8 relative overflow-hidden">
                        <img 
                            src="imgs/f9b03e7902d5cd2d6d59dd7eea390ca8.jpg"
                            alt="Sri Lanka Tourist Map"
                            class="w-full h-auto object-contain"
                        >
                    </div>
                </div>
            </div>
        </div>
    </section>

    <section class="py-20 px-6 bg-white">
        <div class="max-w-7xl mx-auto">
            <div class="text-center mb-4">
                <div class="w-12 h-12 mx-auto mb-6 flex items-center justify-center">
                    <i class="ri-building-line text-primary text-2xl"></i>
                </div>
            </div>
            
            <div class="mb-16">
                <p class="text-primary font-medium mb-4 tracking-wide text-center">LUXURY EXPERIENCE</p>
                <h2 class="text-4xl font-bold text-gray-900 text-center">Our Hotels</h2>
            </div>
            
            <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
                <div class="bg-white rounded-xl overflow-hidden shadow-lg hover:shadow-xl transition-shadow">
                    <div class="relative h-64">
                        <img 
                            src="imgs/9aeb9dd06e5ec3111d6a8e00c7211dde.jpg"
                            alt="Rock de gant Hotel"
                            class="w-full h-full object-cover object-top"
                        >
                        <div class="absolute bottom-4 left-4">
                            <p class="text-white text-sm font-medium">From $190/night</p>
                            <h3 class="text-white text-xl font-bold">Rock de gant</h3>
                        </div>
                    </div>
                </div>
                
                <div class="bg-white rounded-xl overflow-hidden shadow-lg hover:shadow-xl transition-shadow">
                    <div class="relative h-64">
                        <img 
                            src="imgs/f20d82b852859e46a5aea08a6b49dd25.jpg"
                            alt="Serene Bay Retreat"
                            class="w-full h-full object-cover object-top"
                        >
                        <div class="absolute bottom-4 left-4">
                            <p class="text-white text-sm font-medium">From $150/night</p>
                            <h3 class="text-white text-xl font-bold">Serene Bay Retreat</h3>
                        </div>
                    </div>
                </div>
                
                <div class="bg-white rounded-xl overflow-hidden shadow-lg hover:shadow-xl transition-shadow">
                    <div class="relative h-64">
                        <img 
                            src="imgs/69fb2a02b04a9c67ec83ff2b64c1372e.jpg"
                            alt="Coastal Breeze Inn"
                            class="w-full h-full object-cover object-top"
                        >
                        <div class="absolute bottom-4 left-4">
                            <p class="text-white text-sm font-medium">From $120/night</p>
                            <h3 class="text-white text-xl font-bold">Coastal Breeze Inn</h3>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="text-center">
                <button class="bg-primary hover:bg-secondary text-white px-8 py-3 rounded-button font-medium transition-colors whitespace-nowrap !rounded-button">
                    View All Hotels
                </button>
            </div>
        </div>
    </section>

    <footer class="bg-green-900 text-green-900 py-8 relative" style="background-image: url('imgs/Frame 53.png'); background-size: cover; background-position: center;">
        <div class="absolute inset-0 opacity-80 pointer-events-none"></div>
        <div class="max-w-7xl mx-auto px-6 relative z-10">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                <div>
                    <div class="mb-6">
                        <img src="imgs/turtlelogo 1.png" alt="Turtle Isle Ventures Logo" class="h-8">
                    </div>
                    <div class="space-y-2">
                        <p class="flex items-center">
                            <span class="w-5 h-5 flex items-center justify-center mr-3">
                                <i class="ri-map-pin-line"></i>
                            </span>
                            123 Paradise Island Drive
                        </p>
                        <p class="flex items-center">
                            <span class="w-5 h-5 flex items-center justify-center mr-3">
                                <i class="ri-phone-line"></i>
                            </span>
                            +****************
                        </p>
                        <p class="flex items-center">
                            <span class="w-5 h-5 flex items-center justify-center mr-3">
                                <i class="ri-mail-line"></i>
                            </span>
                            <EMAIL>
                        </p>
                    </div>
                </div>
                
                <div>
                    <h3 class="text-lg font-semibold mb-4">Quick Links</h3>
                    <ul class="space-y-2">
                        <li><a href="index.html" class="hover:text-gray-300 transition-colors">Home</a></li>
                        <li><a href="#" class="hover:text-gray-300 transition-colors">About</a></li>
                        <li><a href="#" class="hover:text-gray-300 transition-colors">Tours</a></li>
                        <li><a href="stay.html" class="hover:text-gray-300 transition-colors">Hotels</a></li>
                        <li><a href="#" class="hover:text-gray-300 transition-colors">Contact</a></li>
                    </ul>
                </div>
                
                <div>
                    <h3 class="text-lg font-semibold mb-4">Services</h3>
                    <ul class="space-y-2">
                        <li><a href="#" class="hover:text-gray-300 transition-colors">Snorkeling Tours</a></li>
                        <li><a href="#" class="hover:text-gray-300 transition-colors">Island Hopping</a></li>
                        <li><a href="#" class="hover:text-gray-300 transition-colors">Spa Treatments</a></li>
                        <li><a href="#" class="hover:text-gray-300 transition-colors">Dining</a></li>
                        <li><a href="#" class="hover:text-gray-300 transition-colors">Adventure Sports</a></li>
                    </ul>
                </div>
                
                <div>
                    <h3 class="text-lg font-semibold mb-4">Follow Us</h3>
                    <div class="flex space-x-4">
                        <div class="w-8 h-8 flex items-center justify-center">
                            <i class="ri-facebook-line ri-lg hover:text-gray-300 transition-colors cursor-pointer"></i>
                        </div>
                        <div class="w-8 h-8 flex items-center justify-center">
                            <i class="ri-twitter-line ri-lg hover:text-gray-300 transition-colors cursor-pointer"></i>
                        </div>
                        <div class="w-8 h-8 flex items-center justify-center">
                            <i class="ri-instagram-line ri-lg hover:text-gray-300 transition-colors cursor-pointer"></i>
                        </div>
                        <div class="w-8 h-8 flex items-center justify-center">
                            <i class="ri-youtube-line ri-lg hover:text-gray-300 transition-colors cursor-pointer"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </footer>
    <div class="bg-green-900 border-t border-green-800 py-4 text-center">
        <p class="text-gray-300 m-0">&copy; 2025 Turtle Isle Ventures. All rights reserved.</p>
    </div>

    <script id="mobile-menu-toggle">
        document.addEventListener('DOMContentLoaded', function() {
            const mobileMenuBtn = document.getElementById('mobile-menu-btn');
            const mobileMenu = document.getElementById('mobile-menu');
            
            mobileMenuBtn.addEventListener('click', function() {
                mobileMenu.classList.toggle('hidden');
            });
        });
    </script>

    <script id="form-interactions">
        document.addEventListener('DOMContentLoaded', function() {
            const selectElement = document.querySelector('.custom-select');
            const textareaElement = document.querySelector('textarea');
            const submitBtn = document.querySelector('button[type="button"]');
            
            selectElement.addEventListener('change', function() {
                console.log('Selected:', this.value);
            });
            
            textareaElement.addEventListener('input', function() {
                this.style.height = 'auto';
                this.style.height = this.scrollHeight + 'px';
            });
        });
    </script>
</body>
</html>