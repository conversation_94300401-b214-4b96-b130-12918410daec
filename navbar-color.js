// Navbar color detection: white text on hero, dark green on light backgrounds
(function() {
    const nav = document.getElementById('mainNav');
    const navLinks = nav.querySelectorAll('a');
    const languageButton = nav.querySelector('button[type="button"]');
    const hero = document.querySelector('.hero-bg, .hero-carousel-section');

    function setNavColor(isLightBg) {
        const header = document.querySelector('header[data-navbar]');

        navLinks.forEach(link => {
            if (isLightBg) {
                link.className = link.className.replace(/text-white|text-gray-200|hover:text-gray-200/g, '');
                link.classList.add('text-green-800', 'hover:text-green-600', 'font-medium');
            } else {
                link.className = link.className.replace(/text-green-800|text-green-600|hover:text-green-600|font-medium/g, '');
                link.classList.add('text-white', 'hover:text-gray-200');
            }
        });

        // Update language selector button
        if (languageButton) {
            if (isLightBg) {
                languageButton.className = languageButton.className.replace(/text-white/g, '');
                languageButton.classList.add('text-green-800', 'font-medium');
            } else {
                languageButton.className = languageButton.className.replace(/text-green-800|font-medium/g, '');
                languageButton.classList.add('text-white');
            }
        }

        // Update navbar background
        if (header) {
            if (isLightBg) {
                header.classList.remove('navbar-dark');
                header.classList.add('navbar-light');
            } else {
                header.classList.remove('navbar-light');
                header.classList.add('navbar-dark');
            }
        }
    }
    function getLuminance(r, g, b) {
        const a = [r, g, b].map(function(v) {
            v = v / 255;
            return v <= 0.03928 ? v / 12.92 : Math.pow((v + 0.055) / 1.055, 2.4);
        });
        return 0.2126 * a[0] + 0.7152 * a[1] + 0.0722 * a[2];
    }
    function isBodyLightBg() {
        const bg = window.getComputedStyle(document.body).backgroundColor;
        if (!bg) return false;
        const rgb = bg.match(/\d+\.?\d*/g);
        if (!rgb) return false;
        const r = +rgb[0], g = +rgb[1], b = +rgb[2];
        const a = rgb[3] !== undefined ? +rgb[3] : 1;
        if (a < 0.5) return false;
        const luminance = getLuminance(r, g, b);
        return luminance > 0.7;
    }
    function isHeroVisible() {
        if (!hero) return false;
        const rect = hero.getBoundingClientRect();
        // Consider hero visible if its bottom is below the navbar's bottom
        return rect.bottom > nav.getBoundingClientRect().bottom;
    }

    function updateNavColor() {
        // Check if we're on a page with a hero section
        if (hero && isHeroVisible()) {
            setNavColor(false); // Always white on hero/carousel
        } else {
            // On light backgrounds or when scrolled past hero, use dark green
            setNavColor(true);
        }
    }
    window.addEventListener('scroll', updateNavColor);
    window.addEventListener('resize', updateNavColor);
    document.addEventListener('DOMContentLoaded', updateNavColor);
    updateNavColor();
})(); 