// Navbar color detection: white text on hero, green on light backgrounds
(function() {
    const nav = document.getElementById('mainNav');
    const navLinks = nav.querySelectorAll('a');
    const logo = nav.querySelector('div');
    const hero = document.querySelector('.hero-bg');
    function setNavColor(isLightBg) {
        navLinks.forEach(link => {
            link.style.color = isLightBg ? '#032204' : '#fff';
        });
        if (logo) logo.style.color = isLightBg ? '#032204' : '#fff';
    }
    function getLuminance(r, g, b) {
        const a = [r, g, b].map(function(v) {
            v = v / 255;
            return v <= 0.03928 ? v / 12.92 : Math.pow((v + 0.055) / 1.055, 2.4);
        });
        return 0.2126 * a[0] + 0.7152 * a[1] + 0.0722 * a[2];
    }
    function isBodyLightBg() {
        const bg = window.getComputedStyle(document.body).backgroundColor;
        if (!bg) return false;
        const rgb = bg.match(/\d+\.?\d*/g);
        if (!rgb) return false;
        const r = +rgb[0], g = +rgb[1], b = +rgb[2];
        const a = rgb[3] !== undefined ? +rgb[3] : 1;
        if (a < 0.5) return false;
        const luminance = getLuminance(r, g, b);
        return luminance > 0.7;
    }
    function isHeroVisible() {
        if (!hero) return false;
        const rect = hero.getBoundingClientRect();
        // Consider hero visible if its bottom is below the navbar's bottom
        return rect.bottom > nav.getBoundingClientRect().bottom;
    }
    function updateNavColor() {
        if (isHeroVisible()) {
            setNavColor(false); // Always white on hero
        } else {
            setNavColor(isBodyLightBg());
        }
    }
    window.addEventListener('scroll', updateNavColor);
    window.addEventListener('resize', updateNavColor);
    document.addEventListener('DOMContentLoaded', updateNavColor);
    updateNavColor();
})(); 