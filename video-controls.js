// Video section controls
document.addEventListener('DOMContentLoaded', function() {
    const videoContainer = document.querySelector('.video-container');
    const video = document.querySelector('.video-container video');
    const videoOverlay = document.querySelector('.video-overlay');
    const playButton = document.querySelector('.play-button');
    
    if (video && videoOverlay && playButton) {
        // Handle play button click
        playButton.addEventListener('click', function() {
            if (video.paused) {
                video.play();
                videoOverlay.style.opacity = '0';
            } else {
                video.pause();
                videoOverlay.style.opacity = '1';
            }
        });
        
        // Handle video click
        video.addEventListener('click', function() {
            if (video.paused) {
                video.play();
                videoOverlay.style.opacity = '0';
            } else {
                video.pause();
                videoOverlay.style.opacity = '1';
            }
        });
        
        // Show overlay when video is paused
        video.addEventListener('pause', function() {
            videoOverlay.style.opacity = '1';
        });
        
        // Hide overlay when video is playing
        video.addEventListener('play', function() {
            videoOverlay.style.opacity = '0';
        });
        
        // Handle video end
        video.addEventListener('ended', function() {
            videoOverlay.style.opacity = '1';
        });
    }
    
    // Enhanced carousel functionality
    const carousel = document.querySelector('#heroCarousel');
    if (carousel) {
        // Add custom event listeners for carousel
        carousel.addEventListener('slide.bs.carousel', function (e) {
            // Add any custom slide transition effects here
            console.log('Carousel sliding to slide:', e.to);
        });
        
        carousel.addEventListener('slid.bs.carousel', function (e) {
            // Add any post-slide effects here
            console.log('Carousel slid to slide:', e.to);
        });
    }
});
