<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Explore Sri Lanka: Tourist Routes</title>
    <script src="https://cdn.tailwindcss.com/3.4.16"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Pacifico&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/remixicon/4.6.0/remixicon.min.css">
    <link rel="stylesheet" href="styles.css">">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#10b981',
                        secondary: '#e6f7e6'
                    },
                    borderRadius: {
                        'none': '0px',
                        'sm': '4px',
                        DEFAULT: '8px',
                        'md': '12px',
                        'lg': '16px',
                        'xl': '20px',
                        '2xl': '24px',
                        '3xl': '32px',
                        'full': '9999px',
                        'button': '8px'
                    }
                }
            }
        }
    </script>
    <style>
        :where([class^="ri-"])::before { content: "\f3c2"; }
        .hero-bg {
            background-image: url('imgs/b527ae6397f80bbc0d81fbd09f19f333.jpg');
            background-size: cover;
            background-position: center;
            background-repeat: no-repeat;
        }

        .map-container {
            position: relative;
            overflow: hidden;
            background-color: transparent;
        }
        .map-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-image: url('imgs/pexels-ollivves-1078983.jpg');
            background-size: cover;
            background-position: center;
            background-repeat: no-repeat;
            opacity: 0.3;
            z-index: 1;
        }

        .map-pin {
            position: absolute;
            z-index: 4;
            animation: bounce 2s infinite;
        }

        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% {
                transform: translateY(0);
            }
            40% {
                transform: translateY(-10px);
            }
            60% {
                transform: translateY(-5px);
            }
        }

        .route-line {
            position: absolute;
            z-index: 3;
            border: 2px dashed rgba(255, 255, 255, 0.8);
            animation: dash 3s linear infinite;
        }

        @keyframes dash {
            to {
                stroke-dashoffset: -20;
            }
        }
    </style>
</head>
<body class="bg-white">
    <header class="fixed top-0 left-0 right-0 w-full z-50 bg-white/80 backdrop-blur-lg" data-navbar>
        <nav id="mainNav" class="flex items-center justify-between px-6 py-4 max-w-7xl mx-auto">
            <div>
                <img src="imgs/turtlelogo 1.png" alt="Turtle Isle Ventures Logo" class="h-10">
            </div>
           <div class="hidden md:flex items-center space-x-8">
                <a href="index.html" class="text-green-800 hover:text-green-600 transition-colors font-medium">Home</a>

                <a href="stay.html" class="text-green-800 hover:text-green-600 transition-colors font-medium">Stay</a>
                <a href="explore.html" class="text-green-800 hover:text-green-600 transition-colors font-medium">Explore</a>
                <a href="contact.html" class="text-green-800 hover:text-green-600 transition-colors font-medium">Contact</a>
                <a href="#" class="text-green-800 hover:text-green-600 transition-colors font-medium">About Us</a>
            </div>
            <div class="flex items-center space-x-4">
                <!-- Custom language selector -->
                <div class="relative group">
                    <button type="button" class="flex items-center bg-white/20 text-green-800 text-sm rounded px-3 py-1 outline-none border-none focus:ring-2 focus:ring-primary gap-2 min-w-[120px] font-medium">
                        <img src="imgs/us-flag.png" alt="English" class="w-6 h-6 rounded-full object-cover" />
                        <span>English</span>
                        <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
                            <path d="M6 9l6 6 6-6"/>
                        </svg>
                    </button>
                    <!-- Dropdown menu, hidden by default, shown on hover/focus -->
                    <div class="absolute left-0 mt-2 w-32 bg-white rounded shadow-lg z-10 hidden group-hover:block group-focus:block">
                        <button class="flex items-center w-full px-3 py-2 text-gray-800 hover:bg-gray-100 gap-2">
                            <img src="imgs/us-flag.png" alt="English" class="w-5 h-5 rounded-full object-cover" />
                            English
                        </button>
                        <button class="flex items-center w-full px-3 py-2 text-gray-800 hover:bg-gray-100 gap-2">
                            <img src="imgs/de-flag.png" alt="Deutsch" class="w-5 h-5 rounded-full object-cover" />
                            Deutsch
                        </button>
                    </div>
                </div>
            </div>
        </nav>
    </header>

    <main class="pt-20">
        <section class="hero-bg relative min-h-[400px] flex items-center justify-center ">
            <div class="absolute inset-0 bg-gradient-to-r from-secondary/90 to-secondary/60"></div>
            <div class="relative z-10 text-center px-6 max-w-4xl mx-auto">
                <p class="text-sm font-medium text-gray-600 mb-4 tracking-wide uppercase">Explore</p>
                <h1 class="text-4xl md:text-5xl lg:text-6xl font-bold text-gray-900 mb-6 leading-tight">
                    Explore Sri Lanka:<br>
                    Tourist Routes
                </h1>
                <p class="text-lg text-gray-700 max-w-2xl mx-auto leading-relaxed">
                    Uncover Sri Lanka's breathtaking landscapes and cultural gems with curated travel routes. From ancient cities to coastal escapes and misty hill country, each path reveals a unique side of this island paradise.
                </p>
            </div>
        </section>

        <section class="py-16 px-20 mx-20 max-w-8xl mx-auto global-bg">
            <div class="mb-8 animate-on-scroll animate-fade-in-up">
                <button class="flex items-center space-x-2 text-gray-600 hover:text-primary transition-colors">
                    <div class="w-4 h-4 flex items-center justify-center">
                        <i class="ri-arrow-left-line"></i>
                    </div>
                    <span class="text-sm">Back to itineraries</span>
                </button>
            </div>

            <div class="grid lg:grid-cols-2 gap-12 items-start">
                <div class="animate-on-scroll animate-fade-in-left">
                    <p class="text-sm font-medium text-gray-500 mb-4 tracking-wide uppercase">Recommended Tours</p>
                    <h2 class="text-3xl font-bold text-gray-900 mb-8">Adventures curated for you</h2>
                    
                    <div class="space-y-4">
                        <a href="explore3.html" class="w-full flex items-center justify-between p-4 rounded-lg border border-gray-200 hover:border-primary hover:bg-gray-50 transition-all group !rounded-button whitespace-nowrap animate-on-scroll animate-fade-in-up animate-delay-200">
                            <span class="text-gray-800 font-medium">Kandy and hill country</span>
                            <div class="w-5 h-5 flex items-center justify-center">
                                <i class="ri-arrow-right-line text-gray-400 group-hover:text-primary transition-colors"></i>
                            </div>
                        </a>

                        <button class="w-full flex items-center justify-between p-4 rounded-lg bg-primary text-white transition-all group !rounded-button whitespace-nowrap animate-on-scroll animate-fade-in-up animate-delay-300">
                            <span class="font-medium">South coast and wildlife</span>
                            <div class="w-5 h-5 flex items-center justify-center">
                                <i class="ri-arrow-right-line text-white"></i>
                            </div>
                        </button>

                        <button class="w-full flex items-center justify-between p-4 rounded-lg border border-gray-200 hover:border-primary hover:bg-gray-50 transition-all group !rounded-button whitespace-nowrap animate-on-scroll animate-fade-in-up animate-delay-400">
                            <span class="text-gray-800 font-medium">Cultural triangle and east</span>
                            <div class="w-5 h-5 flex items-center justify-center">
                                <i class="ri-arrow-right-line text-gray-400 group-hover:text-primary transition-colors"></i>
                            </div>
                        </button>

                        <button class="w-full flex items-center justify-between p-4 rounded-lg border border-gray-200 hover:border-primary hover:bg-gray-50 transition-all group !rounded-button whitespace-nowrap animate-on-scroll animate-fade-in-up animate-delay-500">
                            <span class="text-gray-800 font-medium">North central and ancient history</span>
                            <div class="w-5 h-5 flex items-center justify-center">
                                <i class="ri-arrow-right-line text-gray-400 group-hover:text-primary transition-colors"></i>
                            </div>
                        </button>

                        <button class="w-full flex items-center justify-between p-4 rounded-lg border border-gray-200 hover:border-primary hover:bg-gray-50 transition-all group !rounded-button whitespace-nowrap animate-on-scroll animate-fade-in-up animate-delay-600">
                            <span class="text-gray-800 font-medium">Wildlife and northwest coast</span>
                            <div class="w-5 h-5 flex items-center justify-center">
                                <i class="ri-arrow-right-line text-gray-400 group-hover:text-primary transition-colors"></i>
                            </div>
                        </button>
                    </div>
                </div>
                
                <div class="map-container rounded-2xl h-96 lg:h-[500px] relative overflow-hidden">
                    <div class="map-overlay"></div>
                    <div class="map-content">

                        <!-- Map pins for different locations -->
                        <div class="map-pin" style="top: 20%; left: 30%;">
                            <div class="bg-white rounded-full p-2 shadow-lg">
                                <i class="ri-map-pin-fill text-primary text-xl"></i>
                            </div>
                        </div>

                        <div class="map-pin" style="top: 40%; right: 25%; animation-delay: 0.5s;">
                            <div class="bg-white rounded-full p-2 shadow-lg">
                                <i class="ri-map-pin-fill text-red-500 text-xl"></i>
                            </div>
                        </div>

                        <div class="map-pin" style="bottom: 30%; left: 40%; animation-delay: 1s;">
                            <div class="bg-white rounded-full p-2 shadow-lg">
                                <i class="ri-map-pin-fill text-blue-500 text-xl"></i>
                            </div>
                        </div>

                        <div class="map-pin" style="top: 60%; right: 40%; animation-delay: 1.5s;">
                            <div class="bg-white rounded-full p-2 shadow-lg">
                                <i class="ri-map-pin-fill text-yellow-500 text-xl"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- <section class="py-16 px-6 bg-gray-50">
            <div class="max-w-7xl mx-auto">
                <div class="text-center mb-4">
                    <div class="w-8 h-8 flex items-center justify-center mx-auto mb-4">
                        <i class="ri-leaf-line text-primary text-2xl"></i>
                    </div>
                </div>
                
                <div class="mb-12">
                    <p class="text-sm font-medium text-gray-500 mb-4 tracking-wide uppercase text-center">Luxury Experience</p>
                    <h2 class="text-3xl font-bold text-gray-900 text-center">Our Hotels</h2>
                </div>
                
                <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
                    <div class="bg-white rounded-2xl overflow-hidden shadow-sm hover:shadow-lg transition-shadow">
                        <div class="h-48 bg-cover bg-center" style="background-image: url('imgs/64f807cd38a6aa23f283b3c725cd59ca.jpg');">
                        </div>
                        <div class="p-6">
                            <p class="text-sm text-gray-600 mb-2">From $190/night</p>
                            <h3 class="text-xl font-semibold text-gray-900">Rock de gant</h3>
                        </div>
                    </div>
                    
                    <div class="bg-white rounded-2xl overflow-hidden shadow-sm hover:shadow-lg transition-shadow">
                        <div class="h-48 bg-cover bg-center" style="background-image: url('imgs/bdb4e7e7554e2fb845baa0d97169c144.jpg');">
                        </div>
                        <div class="p-6">
                            <p class="text-sm text-gray-600 mb-2">From $150/night</p>
                            <h3 class="text-xl font-semibold text-gray-900">Serene Bay Retreat</h3>
                        </div>
                    </div>
                    
                    <div class="bg-white rounded-2xl overflow-hidden shadow-sm hover:shadow-lg transition-shadow">
                        <div class="h-48 bg-cover bg-center" style="background-image: url('imgs/8ca8f7522e4619f2ed6d0e97e51ae37c.jpg');">
                        </div>
                        <div class="p-6">
                            <p class="text-sm text-gray-600 mb-2">From $120/night</p>
                            <h3 class="text-xl font-semibold text-gray-900">Coastal Breeze Inn</h3>
                        </div>
                    </div>
                </div>
                
                <div class="text-center">
                    <button class="bg-green-900  text-white px-8 py-3 rounded-lg hover:bg-primary/90 transition-colors font-medium !rounded-button whitespace-nowrap">
                        View All Hotels
                    </button>
                </div>
            </div>
        </section> -->
         <!-- Video Section -->
    <section class="py-16 global-bg pattern-overlay">
        <div class="max-w-7xl mx-auto px-6">
            <div class="text-center mb-12 animate-on-scroll animate-fade-in-up">
                <h2 class="text-4xl font-serif text-gray-800 mb-4">Experience Paradise</h2>
                <p class="text-gray-600 text-lg max-w-2xl mx-auto">
                    Watch the breathtaking beauty of our tropical paradise come to life
                </p>
            </div>

            <div class="relative overflow-hidden rounded-2xl animate-on-scroll animate-fade-in-up animate-delay-200">
                <div class="video-container relative h-96 md:h-[500px] bg-black rounded-2xl">
                    <video
                        class="w-full h-full object-cover rounded-2xl"
                        controls
                        autoplay
                        muted
                        loop
                        preload="metadata">
                        <source src="imgs/sl-vid1.mp4" type="video/mp4">
                        <!-- <source src="videos/turtle-paradise.webm" type="video/webm"> -->
                        <!-- Fallback content -->
                    </video>
                </div>
            </div>
        </div>
    </section>
    </main>

    <footer class="bg-green-900 text-green-900 py-8 relative" style="background-image: url('imgs/Frame 53.png'); background-size: cover; background-position: center;">
        <div class="absolute inset-0 opacity-80 pointer-events-none"></div>
        <div class="max-w-7xl mx-auto px-6 relative z-10">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                <div>
                    <div class="mb-6">
                        <img src="imgs/turtlelogo 1.png" alt="Turtle Isle Ventures Logo" class="h-8">
                    </div>
                    <div class="space-y-2">
                        <p class="flex items-center">
                            <span class="w-5 h-5 flex items-center justify-center mr-3">
                                <i class="ri-map-pin-line"></i>
                            </span>
                            123 Paradise Island Drive
                        </p>
                        <p class="flex items-center">
                            <span class="w-5 h-5 flex items-center justify-center mr-3">
                                <i class="ri-phone-line"></i>
                            </span>
                            +****************
                        </p>
                        <p class="flex items-center">
                            <span class="w-5 h-5 flex items-center justify-center mr-3">
                                <i class="ri-mail-line"></i>
                            </span>
                            <EMAIL>
                        </p>
                    </div>
                </div>
                
                <div>
                    <h3 class="text-lg font-semibold mb-4">Quick Links</h3>
                    <ul class="space-y-2">
                        <li><a href="index.html" class="hover:text-gray-300 transition-colors">Home</a></li>
                        <li><a href="#" class="hover:text-gray-300 transition-colors">About</a></li>
                        <li><a href="#" class="hover:text-gray-300 transition-colors">Tours</a></li>
                        <li><a href="stay.html" class="hover:text-gray-300 transition-colors">Hotels</a></li>
                        <li><a href="#" class="hover:text-gray-300 transition-colors">Contact</a></li>
                    </ul>
                </div>
                
                <div>
                    <h3 class="text-lg font-semibold mb-4">Services</h3>
                    <ul class="space-y-2">
                        <li><a href="#" class="hover:text-gray-300 transition-colors">Snorkeling Tours</a></li>
                        <li><a href="#" class="hover:text-gray-300 transition-colors">Island Hopping</a></li>
                        <li><a href="#" class="hover:text-gray-300 transition-colors">Spa Treatments</a></li>
                        <li><a href="#" class="hover:text-gray-300 transition-colors">Dining</a></li>
                        <li><a href="#" class="hover:text-gray-300 transition-colors">Adventure Sports</a></li>
                    </ul>
                </div>
                
                <div>
                    <h3 class="text-lg font-semibold mb-4">Follow Us</h3>
                    <div class="flex space-x-4">
                        <div class="w-8 h-8 flex items-center justify-center">
                            <i class="ri-facebook-line ri-lg hover:text-gray-300 transition-colors cursor-pointer"></i>
                        </div>
                        <div class="w-8 h-8 flex items-center justify-center">
                            <i class="ri-twitter-line ri-lg hover:text-gray-300 transition-colors cursor-pointer"></i>
                        </div>
                        <div class="w-8 h-8 flex items-center justify-center">
                            <i class="ri-instagram-line ri-lg hover:text-gray-300 transition-colors cursor-pointer"></i>
                        </div>
                        <div class="w-8 h-8 flex items-center justify-center">
                            <i class="ri-youtube-line ri-lg hover:text-gray-300 transition-colors cursor-pointer"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </footer>
    <div class="bg-green-900 border-t border-green-800 py-4 text-center">
        <p class="text-gray-300 m-0">&copy; 2025 Turtle Isle Ventures. All rights reserved.</p>
    </div>

    <script id="mobile-menu-toggle">
        document.addEventListener('DOMContentLoaded', function() {
            const mobileMenuBtn = document.getElementById('mobile-menu-btn');
            const mobileMenu = document.getElementById('mobile-menu');
            
            mobileMenuBtn.addEventListener('click', function() {
                mobileMenu.classList.toggle('hidden');
            });
        });
    </script>

    <script id="tour-selection">
        document.addEventListener('DOMContentLoaded', function() {
            const tourButtons = document.querySelectorAll('button[class*="border-gray-200"], button[class*="bg-primary"]');
            
            tourButtons.forEach(button => {
                button.addEventListener('click', function() {
                    tourButtons.forEach(btn => {
                        btn.classList.remove('bg-primary', 'text-white');
                        btn.classList.add('border', 'border-gray-200', 'hover:border-primary', 'hover:bg-gray-50');
                        btn.querySelector('span').classList.remove('text-white');
                        btn.querySelector('span').classList.add('text-gray-800', 'font-medium');
                        btn.querySelector('i').classList.remove('text-white');
                        btn.querySelector('i').classList.add('text-gray-400', 'group-hover:text-primary');
                    });
                    
                    this.classList.remove('border', 'border-gray-200', 'hover:border-primary', 'hover:bg-gray-50');
                    this.classList.add('bg-primary', 'text-white');
                    this.querySelector('span').classList.remove('text-gray-800', 'font-medium');
                    this.querySelector('span').classList.add('text-white', 'font-medium');
                    this.querySelector('i').classList.remove('text-gray-400', 'group-hover:text-primary');
                    this.querySelector('i').classList.add('text-white');
                });
            });
        });
    </script>
    <script src="animations.js"></script>
</body>
</html>