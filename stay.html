<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Coastal Breeze Inn - Luxury Beachfront Resort</title>
    <script src="https://cdn.tailwindcss.com/3.4.16"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Pacifico&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/remixicon/4.6.0/remixicon.min.css">
    <link rel="stylesheet" href="styles.css">">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#047857',
                        secondary: '#10b981'
                    },
                    borderRadius: {
                        'none': '0px',
                        'sm': '4px',
                        DEFAULT: '8px',
                        'md': '12px',
                        'lg': '16px',
                        'xl': '20px',
                        '2xl': '24px',
                        '3xl': '32px',
                        'full': '9999px',
                        'button': '8px'
                    }
                }
            }
        }
    </script>
    <style>
        :where([class^="ri-"])::before {
            content: "\f3c2";
        }
    </style>
</head>
<body class="bg-white">
    <!-- Hero Section -->
    <section class="relative min-h-screen flex items-center justify-center" style="background-image: url('imgs/a231faf0bf101c61519cbae3f2b019b1.jpg'); background-size: cover; background-position: center;">
            <header class="fixed top-0 left-0 right-0 w-full z-50 bg-white/10 backdrop-blur-lg" data-navbar>
                <nav id="mainNav" class="flex items-center justify-between px-6 py-4 max-w-7xl mx-auto">
                    <div>
                        <img src="imgs/turtlelogo 1.png" alt="Turtle Isle Ventures Logo" class="h-10">
                    </div>
                <div class="hidden md:flex items-center space-x-8">
                        <a href="index.html" class="text-white hover:text-gray-200 transition-colors">Home</a>

                        <a href="stay.html" class="text-white hover:text-gray-200 transition-colors">Stay</a>
                        <a href="explore.html" class="text-white hover:text-gray-200 transition-colors">Explore</a>
                        <a href="contact.html" class="text-white hover:text-gray-200 transition-colors">Contact</a>
                        <a href="#" class="text-white hover:text-gray-200 transition-colors">About Us</a>
                    </div>
                    <div class="flex items-center space-x-4">
                        <!-- Custom language selector -->
                        <div class="relative group">
                            <button type="button" class="flex items-center bg-white/20 text-white text-sm rounded px-3 py-1 outline-none border-none focus:ring-2 focus:ring-primary gap-2 min-w-[140px]">
                                <img src="imgs/us-flag.png" alt="English(US)" class="w-6 h-6 rounded-full object-cover" />
                                <span>English(US)</span>
                                <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
                                    <path d="M6 9l6 6 6-6"/>
                                </svg>
                            </button>
                            <!-- Dropdown menu, hidden by default, shown on hover/focus -->
                            <div class="absolute left-0 mt-2 w-40 bg-white rounded shadow-lg z-10 hidden group-hover:block group-focus:block">
                                <button class="flex items-center w-full px-3 py-2 text-gray-800 hover:bg-gray-100 gap-2">
                                    <img src="imgs/us-flag.png" alt="English(US)" class="w-5 h-5 rounded-full object-cover" />
                                    English(US)
                                </button>
                                <button class="flex items-center w-full px-3 py-2 text-gray-800 hover:bg-gray-100 gap-2">
                                    <img src="imgs/es-flag.png" alt="Español" class="w-5 h-5 rounded-full object-cover" />
                                    Español
                                </button>
                                <button class="flex items-center w-full px-3 py-2 text-gray-800 hover:bg-gray-100 gap-2">
                                    <img src="imgs/fr-flag.png" alt="Français" class="w-5 h-5 rounded-full object-cover" />
                                    Français
                                </button>
                                <!-- Add more languages/flags as needed -->
                            </div>
                        </div>
                        <button class="bg-green-900 text-white px-6 py-2 !rounded-button hover:bg-primary/90 transition-colors whitespace-nowrap">
                            Book Now
                        </button>
                    </div>
                </nav>
            </header>

        <!-- Hero Content -->
        <div class="relative z-10 text-center text-white max-w-4xl mx-auto px-6">
            <h1 class="text-5xl md:text-7xl font-bold mb-6">Coastal Breeze Inn</h1>
            <p class="text-xl md:text-2xl mb-8 max-w-2xl mx-auto leading-relaxed">
                Experience a luxury coastal retreat where modern elegance meets natural beauty. 
                Discover your perfect sanctuary overlooking pristine beaches and endless horizons.
            </p>
            <button class="text-white bg-green-900 px-8 py-4 text-lg rounded-full hover:bg-green-600 transition-colors whitespace-nowrap">
                Explore Our Rooms
            </button>
        </div>
    </section>

    <!-- Simple and Elegant Section -->
    <section class="py-20 bg-white second-page-bg ">
        <div class="max-w-6xl mx-auto px-6">
            <div class="text-center mb-16">
                <h2 class="text-4xl font-bold text-gray-900 mb-6">Simple and elegant</h2>
                <p class="text-lg text-gray-600 max-w-3xl mx-auto leading-relaxed">
                    At our beachfront sanctuary, every detail has been thoughtfully curated to create an atmosphere of refined tranquility. 
                    From our minimalist design philosophy to our commitment to sustainable luxury, we offer an experience that celebrates 
                    both comfort and conscious living.
                </p>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                <div class="group cursor-pointer">
                    <div class="overflow-hidden rounded-lg">
                        <img src="imgs/8b60b2170de3b7e31cf023e9a0fb294e.jpg" 
                             alt="Infinity Pool" class="w-full h-64 object-cover object-top group-hover:scale-105 transition-transform duration-300">
                    </div>
                </div>
                <div class="group cursor-pointer">
                    <div class="overflow-hidden rounded-lg">
                        <img src="imgs/b8bc212f5c7895b077cbda6c3017368d.jpg" 
                             alt="Ocean View Room" class="w-full h-64 object-cover object-top group-hover:scale-105 transition-transform duration-300">
                    </div>
                </div>
                <div class="group cursor-pointer">
                    <div class="overflow-hidden rounded-lg">
                        <img src="imgs/db7733a8a8dc5e781a874eaa60975491.jpg" 
                             alt="Spa Sanctuary" class="w-full h-64 object-cover object-top group-hover:scale-105 transition-transform duration-300">
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Room Features Section -->
    <section class="py-20 bg-gray-50">
        <div class="max-w-6xl mx-auto px-6">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
                <div>
                    <img src="imgs/48ccc6993ab3a1a431def30442a657f3.jpg" 
                         alt="Luxury Suite" class="w-full h-96 object-cover object-top rounded-lg">
                </div>
                <div>
                    <h2 class="text-4xl font-bold text-gray-900 mb-8">The room is equipped with</h2>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div class="flex items-center space-x-3">
                            <div class="w-6 h-6 flex items-center justify-center text-primary">
                                <i class="ri-wifi-line"></i>
                            </div>
                            <span class="text-gray-700">High-speed Wi-Fi</span>
                        </div>
                        <div class="flex items-center space-x-3">
                            <div class="w-6 h-6 flex items-center justify-center text-primary">
                                <i class="ri-tv-line"></i>
                            </div>
                            <span class="text-gray-700">Smart TV</span>
                        </div>
                        <div class="flex items-center space-x-3">
                            <div class="w-6 h-6 flex items-center justify-center text-primary">
                                <i class="ri-air-conditioner-line"></i>
                            </div>
                            <span class="text-gray-700">Climate Control</span>
                        </div>
                        <div class="flex items-center space-x-3">
                            <div class="w-6 h-6 flex items-center justify-center text-primary">
                                <i class="ri-safe-line"></i>
                            </div>
                            <span class="text-gray-700">In-room Safe</span>
                        </div>
                        <div class="flex items-center space-x-3">
                            <div class="w-6 h-6 flex items-center justify-center text-primary">
                                <i class="ri-cup-line"></i>
                            </div>
                            <span class="text-gray-700">Coffee & Tea</span>
                        </div>
                        <div class="flex items-center space-x-3">
                            <div class="w-6 h-6 flex items-center justify-center text-primary">
                                <i class="ri-shower-line"></i>
                            </div>
                            <span class="text-gray-700">Rain Shower</span>
                        </div>
                        <div class="flex items-center space-x-3">
                            <div class="w-6 h-6 flex items-center justify-center text-primary">
                                <i class="ri-landscape-line"></i>
                            </div>
                            <span class="text-gray-700">Ocean View</span>
                        </div>
                        <div class="flex items-center space-x-3">
                            <div class="w-6 h-6 flex items-center justify-center text-primary">
                                <i class="ri-door-open-line"></i>
                            </div>
                            <span class="text-gray-700">Private Balcony</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <!-- Photo Gallery Section -->
    <section class="py-20 bg-white second-page-bg">
        <div class="max-w-6xl mx-auto px-6">
            <h2 class="text-4xl font-bold text-gray-900 text-center mb-16">Explore the stay</h2>
            <div class="grid grid-cols-2 lg:grid-cols-4 gap-4">
                <div class="group cursor-pointer">
                    <div class="overflow-hidden rounded-lg aspect-square">
                        <img src="imgs/136f34361dac34e4d41f0d414260cd13.jpg" 
                             alt="Fine Dining" class="w-full h-full object-cover object-top group-hover:scale-105 transition-transform duration-300">
                    </div>
                </div>
                <div class="group cursor-pointer">
                    <div class="overflow-hidden rounded-lg aspect-square">
                        <img src="imgs/2afcf818d19617040ea6936c62b5e7cc.jpg" 
                             alt="Elegant Lobby" class="w-full h-full object-cover object-top group-hover:scale-105 transition-transform duration-300">
                    </div>
                </div>
                <div class="group cursor-pointer">
                    <div class="overflow-hidden rounded-lg aspect-square">
                        <img src="imgs/ab29fa49df7ee572dead6a179c74246c.jpg" 
                             alt="Fitness Center" class="w-full h-full object-cover object-top group-hover:scale-105 transition-transform duration-300">
                    </div>
                </div>
                <div class="group cursor-pointer">
                    <div class="overflow-hidden rounded-lg aspect-square">
                        <img src="imgs/6d585ddbc74da0c7bc315611acf35f81.jpg" 
                             alt="Beachfront Terrace" class="w-full h-full object-cover object-top group-hover:scale-105 transition-transform duration-300">
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Experiences Section -->
    <section class="py-20 bg-gray-50">
        <div class="max-w-6xl mx-auto px-6">
            <h2 class="text-4xl font-bold text-gray-900 text-center mb-4">Nearby Experiences</h2>
            <p class="text-lg text-gray-600 text-center mb-16">Discover the natural wonders and adventures that await</p>
            
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-12">
                <div class="bg-white rounded-lg overflow-hidden shadow-lg">
                    <img src="imgs/974963345089dc3b94e2e4cff08643ae.jpg" 
                         alt="Jungle Treks" class="w-full h-64 object-cover object-top">
                    <div class="p-8">
                        <h3 class="text-2xl font-bold text-gray-900 mb-4">Jungle Treks</h3>
                        <p class="text-gray-600 leading-relaxed">
                            Embark on guided adventures through pristine rainforest trails, discovering hidden waterfalls, 
                            exotic wildlife, and breathtaking vistas. Our experienced guides will lead you through nature's 
                            most spectacular displays while ensuring your safety and comfort.
                        </p>
                    </div>
                </div>
                
                <div class="bg-white rounded-lg overflow-hidden shadow-lg">
                    <img src="imgs/6e5a5205890a7792da35e11d64974df7.jpg" 
                         alt="Meditation & Yoga" class="w-full h-64 object-cover object-top">
                    <div class="p-8">
                        <h3 class="text-2xl font-bold text-gray-900 mb-4">Meditation & Yoga</h3>
                        <p class="text-gray-600 leading-relaxed">
                            Find your inner peace with sunrise yoga sessions on pristine beaches and guided meditation 
                            practices in our tranquil gardens. Connect with nature and rejuvenate your mind, body, 
                            and spirit in our dedicated wellness spaces.
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Call-to-Action Section -->
    <section class="relative py-20" style="background-image: url('imgs/ec8fb2e75187c18f8dbd39fd933878f9.jpg'); background-size: cover; background-position: center; background-attachment:scroll;">
        <div class="absolute inset-0 bg-black bg-opacity-40"></div>
        <div class="relative z-10 text-center text-white max-w-4xl mx-auto px-6">
            <h2 class="text-5xl font-bold mb-6">Explore Magical Journey</h2>
            <p class="text-xl mb-8 max-w-2xl mx-auto leading-relaxed">
                Create unforgettable memories in our tropical paradise where luxury meets natural beauty
            </p>
            <button class="bg-green-900 text-white px-8 py-4 text-lg rounded-full hover:bg-secondary/90 transition-colors whitespace-nowrap">
                Start Your Journey
            </button>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-green-900 text-green-900 py-8 relative" style="background-image: url('imgs/Frame 53.png'); background-size: cover; background-position: center;">
        <div class="absolute inset-0 opacity-80 pointer-events-none"></div>
        <div class="max-w-7xl mx-auto px-6 relative z-10">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                <div>
                    <div class="mb-6">
                        <img src="imgs/turtlelogo 1.png" alt="Turtle Isle Ventures Logo" class="h-8">
                    </div>
                    <div class="space-y-2">
                        <p class="flex items-center">
                            <span class="w-5 h-5 flex items-center justify-center mr-3">
                                <i class="ri-map-pin-line"></i>
                            </span>
                            123 Paradise Island Drive
                        </p>
                        <p class="flex items-center">
                            <span class="w-5 h-5 flex items-center justify-center mr-3">
                                <i class="ri-phone-line"></i>
                            </span>
                            +****************
                        </p>
                        <p class="flex items-center">
                            <span class="w-5 h-5 flex items-center justify-center mr-3">
                                <i class="ri-mail-line"></i>
                            </span>
                            <EMAIL>
                        </p>
                    </div>
                </div>
                
                <div>
                    <h3 class="text-lg font-semibold mb-4">Quick Links</h3>
                    <ul class="space-y-2">
                        <li><a href="index.html" class="hover:text-gray-300 transition-colors">Home</a></li>
                        <li><a href="#" class="hover:text-gray-300 transition-colors">About</a></li>
                        <li><a href="#" class="hover:text-gray-300 transition-colors">Tours</a></li>
                        <li><a href="stay.html" class="hover:text-gray-300 transition-colors">Hotels</a></li>
                        <li><a href="#" class="hover:text-gray-300 transition-colors">Contact</a></li>
                    </ul>
                </div>
                
                <div>
                    <h3 class="text-lg font-semibold mb-4">Services</h3>
                    <ul class="space-y-2">
                        <li><a href="#" class="hover:text-gray-300 transition-colors">Snorkeling Tours</a></li>
                        <li><a href="#" class="hover:text-gray-300 transition-colors">Island Hopping</a></li>
                        <li><a href="#" class="hover:text-gray-300 transition-colors">Spa Treatments</a></li>
                        <li><a href="#" class="hover:text-gray-300 transition-colors">Dining</a></li>
                        <li><a href="#" class="hover:text-gray-300 transition-colors">Adventure Sports</a></li>
                    </ul>
                </div>
                
                <div>
                    <h3 class="text-lg font-semibold mb-4">Follow Us</h3>
                    <div class="flex space-x-4">
                        <div class="w-8 h-8 flex items-center justify-center">
                            <i class="ri-facebook-line ri-lg hover:text-gray-300 transition-colors cursor-pointer"></i>
                        </div>
                        <div class="w-8 h-8 flex items-center justify-center">
                            <i class="ri-twitter-line ri-lg hover:text-gray-300 transition-colors cursor-pointer"></i>
                        </div>
                        <div class="w-8 h-8 flex items-center justify-center">
                            <i class="ri-instagram-line ri-lg hover:text-gray-300 transition-colors cursor-pointer"></i>
                        </div>
                        <div class="w-8 h-8 flex items-center justify-center">
                            <i class="ri-youtube-line ri-lg hover:text-gray-300 transition-colors cursor-pointer"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </footer>
    <div class="bg-green-900 border-t border-green-800 py-4 text-center">
        <p class="text-gray-300 m-0">&copy; 2025 Turtle Isle Ventures. All rights reserved.</p>
    </div>

    <script id="navigation-mobile">
        document.addEventListener('DOMContentLoaded', function() {
            const mobileMenuButton = document.querySelector('[data-mobile-menu-button]');
            const mobileMenu = document.querySelector('[data-mobile-menu]');
            
            if (mobileMenuButton && mobileMenu) {
                mobileMenuButton.addEventListener('click', function() {
                    mobileMenu.classList.toggle('hidden');
                });
            }
        });
    </script>

    <script id="smooth-scroll">
        document.addEventListener('DOMContentLoaded', function() {
            const links = document.querySelectorAll('a[href^="#"]');
            
            links.forEach(link => {
                link.addEventListener('click', function(e) {
                    e.preventDefault();
                    const targetId = this.getAttribute('href');
                    const targetElement = document.querySelector(targetId);
                    
                    if (targetElement) {
                        targetElement.scrollIntoView({
                            behavior: 'smooth',
                            block: 'start'
                        });
                    }
                });
            });
        });
    </script>

    <script id="parallax-effect">
        document.addEventListener('DOMContentLoaded', function() {
            const parallaxElements = document.querySelectorAll('[style*="background-attachment: fixed"]');
            
            function updateParallax() {
                const scrolled = window.pageYOffset;
                
                parallaxElements.forEach(element => {
                    const rate = scrolled * -0.5;
                    element.style.transform = `translateY(${rate}px)`;
                });
            }
            
            window.addEventListener('scroll', updateParallax);
        });
    </script>
    <script src="navbar-color.js"></script>
</body>
</html>